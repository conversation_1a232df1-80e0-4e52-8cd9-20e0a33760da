import json
import os
import sys
import aiofiles
from pathlib import Path

sys.path.append(str(Path(__file__).parent.parent.parent.parent))

import traceback
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from app.services.new_query_agent.type import CallStep, PLAN_SCHEMA, CALL_STEP_SCHEMA
from google import genai
from google.genai import types as genai_types
from pathlib import Path
from app.core.config import AUTH_PROPERTIES, SERVICES_PROPERTIES, SETTINGS
from app.common.type import ModelName
from app.core.auth import get_headers
from app.core.rest_utils import RestUtils
import re
import time
from app.services.new_query_agent.utils.expression_evaluation_util import (
    ExpressionEvaluationUtil,
)
from app.services.new_query_agent.dynamic_query import DynamicQueryGenerator
from app.services.new_query_agent.utils.dynamic_query_processor import QueryProcessor
from app.services.gen_ai_service.gen_ai import GenAI

APPLICATION_JSON = "application/json"


class ApiAgent:
    def __init__(
        self,
        api_context_path: str = Path(__file__).parent / "samples" / "api_context.json",
        planing_prompt_path: str = Path(__file__).parent
        / "prompts"
        / "planning_prompt.txt",
        answer_prompt_path: str = Path(__file__).parent
        / "prompts"
        / "answer_prompt.txt",
        context_data: Dict[str, Any] | None = None,
    ):
        """
        Initializes the ApiAgent.

        Args:
            api_list_path: Path to the JSON file containing the API list.
            model_name: The GenAI model to use.
        """

        self.query = ""
        self.api_context_path = api_context_path
        self.planing_prompt_path = planing_prompt_path
        self.planing_prompt_user_input_path = (
            Path(__file__).parent / "prompts" / "planning_prompt_user_input.txt"
        )
        self.answer_prompt_path = answer_prompt_path
        self.answer_prompt_user_input_path = (
            Path(__file__).parent / "prompts" / "answer_prompt_user_input.txt"
        )

        self.api_context = self._load_api_context(api_context_path)
        self.api_list: List[CallStep] = self._load_api_list(self.api_context)
        self.field_bindings = self._get_field_bindings(self.api_context)
        self.table_bindings = self._get_table_bindings(self.api_context)
        self.chart_bindings = self._get_chart_bindings(self.api_context)
        self.section_bindings = self._get_section_bindings(self.api_context)
        self.page_navigation_lookup = self._prepare_page_navigation_lookup(
            self.api_context
        )
        self.endpoint_dependencies_lookup = self._prepare_endpoint_dependencies_lookup(
            self.api_context
        )
        self.context_data = context_data if context_data is not None else {}
        self.page_url_lookup = self._prepare_page_url_lookup_for_endpoints(
            self.api_context
        )

        # Global context data
        self.global_context_data = {}
        self.api_plan_for_global_context_data = (
            self._get_api_plan_for_global_context_data(self.api_context)
        )
        self.field_bindings_for_global_context_data = (
            self._get_field_bindings_for_global_context_data(self.api_context)
        )

        # NOTE: Enabling context cache is slowing down the response time.
        self.ENABLE_CONTEXT_CACHE = False

        # Initialize GenAI client
        try:
            self.genai_client = genai.Client(
                vertexai=True,
                project=AUTH_PROPERTIES.GOOGLE_CLOUD_PROJECT,
                location=AUTH_PROPERTIES.GOOGLE_CLOUD_LOCATION,
            )
        except Exception as e:
            print(f"Error initializing GenAI client: {e}")
            traceback.print_exc()
            # Decide how to handle client initialization failure - maybe raise?
            self.genai_client = None

    def _load_api_context(self, api_context_path: str) -> Dict[str, Any]:
        """Loads the API context from the specified JSON file."""
        if not os.path.exists(api_context_path):
            print(f"Error: API context file not found at {api_context_path}.")
            return {}
        with open(api_context_path, "r") as f:
            return json.load(f)

    def _load_api_list(self, api_context: Dict[str, Any]) -> List[CallStep]:
        """Loads the API list from the specified JSON file."""
        try:
            pages = api_context["pages"]
            return [
                CallStep(**step)
                for page in pages
                for step in page["endpoints"]
                if step["method"] in ["get", "post"]
            ]
        except Exception as e:
            print(f"Error in loading api list: {e}")
            return []



    async def generate_api_plan(self, query: str) -> List[CallStep]:
        """
        Generates a plan of API calls based on the user query and the available APIs using GenAI.
        """
        if not self.genai_client:
            print("Error: GenAI client is not initialized.")
            return []
        if not self.api_list:
            print("Error: API list is empty or not loaded.")
            return []

        # Convert api_list to a string format suitable for the prompt
        api_list_str = json.dumps(
            [step.model_dump() for step in self.api_list], indent=2
        )

        # Load and format the prompt template
        prompt_path = (
            Path(__file__).parent / "prompts" / "planning_multiple_apis_prompt.txt"
        )
        try:
            async with aiofiles.open(prompt_path, "r") as f:
                prompt_template = await f.read()

            prompt = prompt_template.format(api_list=api_list_str, query=query)
        except Exception as e:
            print(f"Error loading prompt template: {e}")
            return []

        # Prepare Configuration
        generation_config = genai_types.GenerateContentConfig(
            temperature=0.0,  # Low temperature for deterministic planning
            response_mime_type=APPLICATION_JSON,
            response_schema=PLAN_SCHEMA,
        )

        # Prepare Content
        contents = [genai_types.Part.from_text(text=prompt)]

        if SETTINGS.DEBUG:
            debug_folder = os.path.join(SETTINGS.WORKSPACE_PATH, "debug", "api_agent")
            if not os.path.exists(debug_folder):
                os.makedirs(debug_folder)
            async with aiofiles.open(
                os.path.join(debug_folder, "prompt_generate_api_plan.txt"), "w"
            ) as f:
                await f.write(prompt)

        # Make the API call
        print(f"Generating API plan for query: {query}")  # Debugging
        try:
            response = await self.genai_client.aio.models.generate_content(
                model=self.model_name, contents=contents, config=generation_config
            )
            plan = response.parsed

            if isinstance(plan, list):  # Basic validation
                print("API plan generated successfully.")
                return [CallStep(**step) for step in plan]
            else:
                print(f"Error: LLM response is not a list: {plan}")
                return []

        except Exception as e:
            print(f"Error during GenAI plan generation: {e}")
            traceback.print_exc()
            return []  # Return empty list on error

    async def _get_token_count(self, text: str, model_name: str) -> int:
        """Get the token count of the prompt. Note: Token count can take a long time to compute (Ex: 1-2 secs)."""
        return self.genai_client.models.count_tokens(
            model=model_name, contents=text
        ).total_tokens

    async def _load_planning_prompt_templates(self) -> Tuple[str, str]:
        """Load planning prompt templates."""
        async with aiofiles.open(self.planing_prompt_path, "r") as f:
            instructions = await f.read()
        async with aiofiles.open(self.planing_prompt_user_input_path, "r") as f:
            user_input = await f.read()
        return instructions, user_input

    def _save_planning_debug_files(self, prompt: str) -> None:
        """Save planning debug files."""
        if not SETTINGS.DEBUG:
            return

        debug_folder = os.path.join(SETTINGS.WORKSPACE_PATH, "debug", "api_agent")
        if not os.path.exists(debug_folder):
            os.makedirs(debug_folder)

        files = {
            "planning_prompt.txt": prompt,
        }

        for filename, content in files.items():
            with open(os.path.join(debug_folder, filename), "w") as f:
                f.write(content)

    async def generate_api_plan_tool_call(self, query: str) -> List[CallStep]:
        """Generates a tool call plan from the available APIs using GenAI."""
        if not self.genai_client or not self.api_list:
            print("Error: GenAI client init failed or API list empty")
            return []

        model = ModelName.GEMINI_20_FLASH
        api_list_str = json.dumps(
            [step.model_dump() for step in self.api_list], indent=2
        )

        try:
            instructions, user_input = await self._load_planning_prompt_templates()
            instructions_prompt = instructions.replace("<api_list>", api_list_str)
            user_input_prompt = user_input.replace(
                "<context_data>", json.dumps(self.global_context_data, indent=2)
            )
            full_prompt = (
                instructions_prompt
                + "\n\n"
                + user_input_prompt
                + "\n\n"
                + self.conversation_history[-1].model_dump_json(indent=2)
            )



            # contents = [
            #     genai_types.Part.from_text(
            #         text=user_input_prompt if context_cache else full_prompt
            #     )
            # ]

            messages = []
            # General instructions for handling the conversation history
            messages.append(
                genai_types.Content(role="user", parts=[{"text": user_input_prompt}])
            )
            # Conversation history
            messages.extend([self.conversation_history[-1]])

            generation_config = genai_types.GenerateContentConfig(
                temperature=0,
                response_mime_type=APPLICATION_JSON,
                response_schema=PLAN_SCHEMA,
                system_instruction=instructions_prompt,
            )

            self._save_planning_debug_files(full_prompt)

            print(f"Generating API plan for query: {query}")
            response = await self.genai_client.aio.models.generate_content(
                model=model,
                contents=messages,
                config=generation_config,
            )


            # Print LLM token usage
            print(
                "Planning step LLM token usage \n : ",
                response.usage_metadata,
            )
            endpoints = {}
            for page in self.api_context["pages"]:
                for endpoint in page["endpoints"]:
                    endpoints[endpoint["id"]] = endpoint
                    
            plan = response.parsed
            if isinstance(plan, list):
                print("API tool call plan generated successfully.")
                output_plan = []
                for step in plan:
                    if step["id"] not in endpoints:
                        print(f"Warning: Step ID {step['id']} not found in API context endpoints.")
                        continue
                    # Merge with endpoint details
                    endpoint_details = endpoints[step["id"]].copy()
                    endpoint_details["query_params"] = step.get("query_params")
                    endpoint_details["path_params"] = step.get("path_params")
                    endpoint_details["requestBody"] = step.get("requestBody")
                    output_plan.append(CallStep(**endpoint_details))
                return output_plan
            else:
                print(f"Error: LLM response is not a list: {plan}")
                return []

        except Exception as e:
            print(f"Error during GenAI plan generation: {e}")
            traceback.print_exc()
            return []

    def parse_to_json(self, input_string):
        print("Input string is", input_string)
        
        # Check if input is already a dictionary string
        if input_string.startswith('{') and input_string.endswith('}'):
            try:
                # Try to parse as JSON first
                result = json.loads(input_string)
                print("Parsed as JSON:", result)
                return result
            except json.JSONDecodeError:
                # If JSON parsing fails, try to evaluate as Python dict
                try:
                    result = eval(input_string)
                    print("Parsed as Python dict:", result)
                    return result
                except:
                    print("Failed to parse dictionary string")
                    return {}
        
        # Original logic for query parameter format
        cleaned_string = input_string.lstrip('_')
        parts = cleaned_string.split('&')
        
        result = {}
        for part in parts:
            if '=' in part:
                key, value = part.split('=', 1)
                key = key.strip()
                value = value.strip().strip('"\'')
                result[key] = value
        
        print("Converted query", result)
        return result



    async def _execute_api_call(self, call_step: CallStep) -> Dict[str, Any] | None:
        """
        Executes an API call and return the response
        """

        headers = await get_headers()
        rest_utils = RestUtils()

        try:
            # Determine base URL based on run mode
            if SERVICES_PROPERTIES.RUN_MODE == "TEST":
                # Use the base URL from services.properties
                base_url = SERVICES_PROPERTIES.BASE_URL
                print(
                    f"Using TEST mode - base URL from services.properties: {base_url}"
                )
            else:
                # Use the base URL from the call step (api_context.json)
                base_url = getattr(call_step, "base_url", None)
                if not base_url:
                    # Fallback to determine base URL based on path if not in call_step
                    if call_step.url.startswith("/api"):
                        base_url = "http://app-data-manager:8002" # NOSONAR
                    elif call_step.url.startswith("/workflow"):
                        base_url = "http://workhorse:8080" # NOSONAR
                    elif call_step.url.startswith("/v1/instances"):
                        base_url = "http://workhorse:8080" # NOSONAR
                    else:
                        base_url = "http://app-data-manager:8002" # NOSONAR
                    print(
                        f"Using PRODUCTION mode - base URL determined by path: {base_url}"
                    )
                else:
                    print(
                        f"Using PRODUCTION mode - base URL from api_context.json: {base_url}"
                    )

            url = base_url + call_step.url
            print(f"Full URL: {url}")
            if call_step.path_params:
                if isinstance(call_step.path_params, str):
                    try:
                        path_params = json.loads(call_step.path_params)
                    except Exception as e:
                        print("Error while creating path parames", e)
                        path_params = self.parse_to_json(call_step.path_params)        
                else:
                    path_params = call_step.path_params
                for key, value in path_params.items():
                    url = url.replace(f"{{{key}}}", value)
            if call_step.query_params:
                print("*****************************", call_step.query_params)
                if isinstance(call_step.query_params, str):
                    try:
                        query_params = json.loads(call_step.query_params)
                    except Exception as e:
                        print("Error while creating query parames", e)
                        query_params = self.parse_to_json(call_step.query_params)
                    
                elif isinstance(call_step.query_params, dict):
                    query_params = call_step.query_params

                url = (
                    url + "?" + "&".join([f"{k}={v}" for k, v in query_params.items()])
                )

            # make the api call
            if call_step.method == "get":
                response = await rest_utils.get(url, headers)
            elif call_step.method == "post":
                response = await rest_utils.post(url, headers, call_step.requestBody)
            else:
                raise ValueError(f"Unsupported method: {call_step.method}")
            return response
        except Exception as e:
            import traceback
            print("Response >>>>>>>>>>>>>>>>", response.content)
            traceback.print_exc()
            print(f"Error executing API call: {str(e)}")
            return None

    async def _process_api_step(
        self, step: CallStep, api_data: Dict[str, Any]
    ) -> Tuple[str, Any]:
        """
        Process a single API call step and return the result variable and response.

        Args:
            step: The API call step to process
            api_data: Dictionary containing results from previous API calls

        Returns:
            Tuple containing (result_variable, response)
        """
        if step.get("type") == "DYNAMIC_QUERY_TOOL":
            dynamic_query_generator = DynamicQueryGenerator(self.context_data)
            dynamic_query = await dynamic_query_generator.generate(self.query)
            if dynamic_query:
                processor = QueryProcessor(default_limit=10)
                dynamic_query = processor.process(dynamic_query)
            step.requestBody = dynamic_query
            step.path_params = {}
            step.query_params = {}

        # Get or generate result variable name
        result_variable = step.get("resultVariable", "")
        if not result_variable:
            result_variable = "result_" + step.get("url").replace("/", "_")

        # Evaluate expressions in the call step
        evaluated_step = ExpressionEvaluationUtil().evaluate_exp_in_call_step(
            step, api_data, skip_error=True
        )
        print(
            "API call step after expression evaluation: ",
            json.dumps(evaluated_step.model_dump(), indent=2),
        )
        
        # Execute the API call
        response = await self._execute_api_call(evaluated_step)

        return result_variable, response

    def _get_system_data(self, headers: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get system data
        """
        return {
            "x-jiffy-user-id": headers.get("x-jiffy-user-id", ""),
        }

    async def execute_api_plan(
        self, plan: List[CallStep], context_data: Dict[str, Any] = {}
    ) -> Dict[str, Any]:
        print("Executing API plan")
        print(
            "API Plan before evaluation: ",
            json.dumps([step.model_dump() for step in plan], indent=2),
        )

        api_data = {}
        api_details = {"api_details": []}
        if context_data:
            api_data.update(context_data)

        for step in plan:
            print(f"Executing step : {step.get('description', step.get('url'))}")

            # Clean up the call step as the plan step path and query params are strings sometime
            step = self._cleanup_call_step(step)
            print(
                "API call step after cleanup: ",
                json.dumps(step.model_dump(), indent=2),
            )

            # Process the API step
            try:
                result_variable, response = await self._process_api_step(step, api_data)
                api_data[result_variable] = response
                api_details["api_details"].append(
                    {
                        "api": step.get("description", step.get("url")),
                        "result_variable": result_variable,
                        "page_url": self._get_page_url_for_endpoint(step),
                        "other_details": {
                            "path_params": step.get("path_params", ""),
                            "query_params": step.get("query_params", ""),
                            "request_body": step.get("requestBody", ""),
                            "response_schema": step.get("responseSchema", ""),
                            "references": step.get("references", ""),
                        },
                    }
                )
                # Execute dependent APIs if any
                if response is not None:
                    dependent_apis = self._get_endpoint_dependencies(step)
                    for dependent_api in dependent_apis:
                        try:
                            result_variable, response = await self._process_api_step(
                                dependent_api, api_data
                            )
                            api_data[result_variable] = response
                            api_details["api_details"].append(
                                {
                                    "api": dependent_api.get(
                                        "description", dependent_api.get("url")
                                    ),
                                    "result_variable": result_variable,
                                    "page_url": self._get_page_url_for_endpoint(
                                        dependent_api
                                    ),
                                }
                            )
                        except Exception as e:
                            print(
                                f"Error processing dependent API {dependent_api.get('description', dependent_api.get('url'))}: {e}"
                            )
            except Exception as e:
                print(
                    f"Error processing API step {step.get('description', step.get('url'))}: {e}"
                )

        print("API plan execution completed")
        return {"api_data": api_data, "api_details": api_details}

    def _extract_json_from_llm_output(self, llm_output: str) -> dict:
        """Extract and validate JSON from LLM output."""
        # print(f"Raw LLM output: {llm_output}")

        try:
            # Convert AIMessage to string if needed
            if hasattr(llm_output, "content"):
                llm_output = llm_output.content

            # Try to find JSON content between triple backticks if present
            if "```json" in llm_output:
                start = llm_output.find("```json") + 7
                end = llm_output.find("```", start)
                if end != -1:
                    llm_output = llm_output[start:end]
            elif "```" in llm_output:
                start = llm_output.find("```") + 3
                end = llm_output.find("```", start)
                if end != -1:
                    llm_output = llm_output[start:end]

            # Clean and format the JSON string
            llm_output = llm_output.strip()

            # Only handle unquoted keys
            llm_output = re.sub(
                r"([{,])\s*([a-zA-Z_][a-zA-Z0-9_]*)\s*:", r'\1"\2":', llm_output
            )
            return json.loads(llm_output)
        except json.JSONDecodeError as e:
            print(f"JSON decode error: {e}")
            print(f"Failed JSON string: {llm_output}")
            return {}
        except Exception as e:
            print(f"Unexpected error: {e}")
            return {}

    def _get_output_formats(self) -> Dict[str, Any]:
        """Get example output formats for different response types."""
        return {
            "singleline": {"html": ""},
            "table": {"headers": [], "types": [], "rows": [[]]},
            "pie_chart": {
                "categoryField": "securityCategory",
                "seriesFields": [
                    {
                        "name": "Total Market Value",
                        "field": "totalMarketValue",
                        "dataType": "Number",
                    }
                ],
                "data": [
                    {"securityCategory": "Options", "totalMarketValue": 1700},
                    {
                        "securityCategory": "Mutual Funds",
                        "totalMarketValue": 757100,
                    },
                    {"securityCategory": "Debt", "totalMarketValue": 148100},
                ],
            },
            "bar_chart": {
                "categoryField": "periodEndDate",
                "seriesFields": [
                    {
                        "name": "Ending Assets",
                        "field": "endingEts",
                        "dataType": "Currency",
                    }
                ],
                "data": [
                    {
                        "endingEts": 49705548.25,
                        "periodEndDate": "2025-02-01",
                        "periodType": "Monthly",
                    },
                    {
                        "endingEts": 818306028.73,
                        "periodEndDate": "2025-03-01",
                        "periodType": "Monthly",
                    },
                    {
                        "endingEts": -64769573.38,
                        "periodEndDate": "2025-04-01",
                        "periodType": "Monthly",
                    },
                ],
            },
        }

    async def _load_prompt_templates(self) -> Tuple[str, str]:
        """Load all required prompt templates."""
        async with aiofiles.open(self.answer_prompt_path, "r") as f:
            instructions = await f.read()
        async with aiofiles.open(self.answer_prompt_user_input_path, "r") as f:
            user_input = await f.read()
        return instructions, user_input

    async def resolve_final_answer(
        self,
        query: str,
        context: Dict[str, Any],
        field_bindings: Dict[str, str],
        table_bindings: Dict[str, Any],
        chart_bindings: Dict[str, Any],
        section_bindings: Dict[str, Any],
        page_navigation_lookup: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Uses the collected API data and field bindings to generate the final formatted output"""
        if not self.genai_client:
            print("Error: GenAI client not initialized.")
            return {"data": []}

        model = ModelName.GEMINI_20_FLASH
        formats = self._get_output_formats()

        try:
            instructions, user_input = await self._load_prompt_templates()

            instructions_prompt = instructions.format(
                field_bindings=json.dumps(field_bindings, indent=2),
                table_bindings=json.dumps(table_bindings, indent=2),
                chart_bindings=json.dumps(chart_bindings, indent=2),
                section_bindings=json.dumps(section_bindings, indent=2),
                page_navigation_lookup=json.dumps(page_navigation_lookup, indent=2),
                **{f"{k}_format": json.dumps(v) for k, v in formats.items()},
            )

            # Api details and response data
            user_input_prompt = user_input.format(
                tool_call_response=json.dumps(context.get("api_details", {}), indent=2),
                context=json.dumps(context.get("api_data", {}), indent=2),
            )

            full_prompt = (
                instructions_prompt
                + "\n\n"
                + user_input_prompt
                + "\n\n"
                + json.dumps(
                    [msg.model_dump() for msg in self.conversation_history], indent=2
                )
            )

            if self.ENABLE_CONTEXT_CACHE:
                time_start_context_cache = time.time()
                context_cache = self._gemini_create_context_cache(
                    cache_name="answer_prompt_cache",
                    model=model,
                    prompt=instructions_prompt,
                )
                time_end_context_cache = time.time()
                print(
                    f"Time taken to create context cache: {time_end_context_cache - time_start_context_cache} seconds"
                )
            else:
                context_cache = None

            # contents = [
            #     genai_types.Part.from_text(
            #         text=user_input_prompt if context_cache else full_prompt
            #     )
            # ]

            messages = []
            # Load config data
            messages.append(
                genai_types.Content(role="user", parts=[{"text": user_input_prompt}])
            )
            # Load conversation history
            messages.extend(self.conversation_history)

            response = await self.genai_client.aio.models.generate_content(
                model=model,
                contents=messages,
                config=genai_types.GenerateContentConfig(
                    temperature=0,
                    response_mime_type=APPLICATION_JSON,
                    system_instruction=instructions_prompt,
                    cached_content=context_cache.name if context_cache else None,
                    # thinking_config=genai_types.ThinkingConfig(thinking_budget=-1),
                ),
            )

            if context_cache:
                print("Context cache details \n : ", response.usage_metadata)

            if SETTINGS.DEBUG:
                self._save_answer_debug_files(full_prompt)

            # Print LLM token usage
            print("Answer step LLM token usage \n : ", response.usage_metadata)

            llm_response = self._extract_json_from_llm_output(response.text)
            # Check for answer section
            answer_section = llm_response.get("answer", {})
            # Check for followup section
            followup_section = llm_response.get("followup", {})

            # Prepare final response
            final_response = []
            if answer_section:
                final_response.append(answer_section)
            if followup_section and len(followup_section.get("options", [])) > 0:
                final_response.append(followup_section)
            return {"data": final_response}

        except Exception as e:
            print(f"Error during GenAI final answer generation: {e}")
            traceback.print_exc()
            return {"data": []}

    def _save_answer_debug_files(self, prompt: str) -> None:
        """Save debug files for prompt inspection."""
        debug_folder = os.path.join(SETTINGS.WORKSPACE_PATH, "debug", "api_agent")
        if not os.path.exists(debug_folder):
            os.makedirs(debug_folder)

        files = {
            "answer_prompt.txt": prompt,
        }

        for filename, content in files.items():
            with open(os.path.join(debug_folder, filename), "w") as f:
                f.write(content)

    def _get_field_bindings(self, api_context: Dict[str, Any]) -> Dict[str, Any]:
        return [
            binding
            for page in api_context.get("pages", [])
            for binding in page.get("fieldBindings", [])
        ]

    def _get_table_bindings(self, api_context: Dict[str, Any]) -> Dict[str, Any]:
        return [
            table
            for page in api_context.get("pages", [])
            for table in page.get("tables", [])
        ]

    def _get_chart_bindings(self, api_context: Dict[str, Any]) -> Dict[str, Any]:
        return [
            chart
            for page in api_context.get("pages", [])
            for chart in page.get("charts", [])
        ]

    def _get_section_bindings(self, api_context: Dict[str, Any]) -> Dict[str, Any]:
        return [
            section
            for page in api_context.get("pages", [])
            for section in page.get("sections", [])
        ]

    def _prepare_page_navigation_lookup(
        self, api_context: Dict[str, Any]
    ) -> Dict[str, str]:
        pages_info = []
        for page in api_context.get("pages", []):
            page_info = {}
            page_info["title"] = page.get("title", "")
            page_info["url"] = page.get("url", "")
            page_info["parameters"] = page.get("parameters", [])
            page_info["description"] = page.get("description", "")
            pages_info.append(page_info)
        return {"pages": pages_info}

    def _prepare_endpoint_dependencies_lookup(
        self, api_context: Dict[str, Any]
    ) -> Dict[str, List[CallStep]]:
        lookup = {}
        for page in api_context.get("pages", []):
            dependencies = page.get("endpointDependencies", [])
            for dependency in dependencies:
                lookup[
                    dependency["endpoint"]["url"]
                    + "_"
                    + dependency["endpoint"]["method"]
                ] = [CallStep(**dep) for dep in dependency["dependencies"]]
        return lookup

    def _get_endpoint_dependencies(
        self, call_step: CallStep
    ) -> Dict[str, List[CallStep]]:
        lookup = self.endpoint_dependencies_lookup
        return lookup.get(call_step.url + "_" + call_step.method, [])

    def _parse_param_to_dict(self, param_value: Any) -> Any:
        """Helper method to parse string parameters into dictionaries."""
        if not isinstance(param_value, str):
            return param_value

        try:
            if param_value.startswith("{"):  # JSON object
                parsed = json.loads(param_value)
                return parsed if isinstance(parsed, dict) else param_value
            elif "&" in param_value:  # URL-encoded query string
                params_dict = {}
                for param in param_value.split("&"):
                    if "=" in param:
                        key, value = param.split("=", 1)
                        params_dict[key] = value
                return params_dict
            return param_value
        except json.JSONDecodeError:
            return param_value

    def _prepare_page_url_lookup_for_endpoints(
        self, api_context: Dict[str, Any]
    ) -> Dict[str, str]:
        lookup = {}
        for page in api_context.get("pages", []):
            endpoints = page.get("endpoints", []) + [
                dependency
                for dependencies in page.get("endpointDependencies", [])
                for dependency in dependencies["dependencies"]
            ]
            for endpoint in endpoints:
                lookup[
                    endpoint.get("url")
                    + "_"
                    + endpoint.get("method")
                    + "_"
                    + endpoint.get("description")
                ] = (page.get("url") if "url" in page else "")
        return lookup

    def _get_page_url_for_endpoint(self, call_step: CallStep) -> str:
        """Get the page URL from the call step."""
        return self.page_url_lookup.get(
            call_step.url + "_" + call_step.method + "_" + call_step.description, ""
        )

    def _cleanup_call_step(self, call_step: CallStep) -> CallStep:
        """Clean up the call step by converting JSON string parameters to dictionaries."""
        try:
            if call_step.path_params:
                call_step.path_params = self._parse_param_to_dict(call_step.path_params)
            if call_step.query_params:
                call_step.query_params = self._parse_param_to_dict(
                    call_step.query_params
                )
            if call_step.requestBody:
                call_step.requestBody = self._parse_param_to_dict(call_step.requestBody)
            return call_step
        except Exception as e:
            print(f"Error cleaning up call step: {e}")
            return call_step

    def _is_valid_response(self, response: Any) -> bool:
        """Check if response is valid and contains non-empty data.

        Valid response must:
        - Be a dictionary
        - Have a 'data' key with a list value
        - The list must contain at least one non-empty item
        """
        return (
            isinstance(response, dict)
            and isinstance(response.get("data"), list)
            and any(bool(item) for item in response.get("data", []))
        )

    def _get_conv_history_for_gemini_models(
        self, query: str
    ) -> list[genai_types.Content]:
        """
        Extract conversation history from query if present.
        Returns a list of message dictionaries.
        Input quest after JSON parsing :
        [
            {
                "actorType": "Human",
                "message": "Hi"
            },
            {
                "actorType": "Machine",
                "message": "Hi, how can I help you?"
            }
        ]
        Expected output:
        [
            {
                "role": "user",
                "parts": [{"text":"Hi"}]
            },
            {
                "role": "model",
                "parts": [{"text":"Hi, how can I help you?"}]
            }
        ]

        """
        default_contents = [
            genai_types.Content(role="user", parts=[genai_types.Part(text=query)])
        ]
        try:
            # Try to parse as JSON string containing list of messages
            messages = json.loads(query)
            contents = []
            if isinstance(messages, list):
                for message in messages:
                    role = (
                        "model" if message.get("actorType", "") == "Machine" else "user"
                    )
                    text = message.get("message", "")
                    contents.append(
                        genai_types.Content(
                            role=role, parts=[genai_types.Part(text=text)]
                        )
                    )
                return contents
            else:
                # If parsed but not a list, wrap in single message
                print(
                    "Input query is not a list conversation messages, considering it as a user message"
                )
                return default_contents

        except json.JSONDecodeError as e:
            print("Query JSON parsing error : ", e)
            # If not valid JSON, treat as single message
            print("Input query is a simple string, considering it as a user message")
            return default_contents

    def _get_api_plan_for_global_context_data(
        self, api_context: Dict[str, Any]
    ) -> List[CallStep]:
        """Get the api plan for the global context data."""
        return [
            CallStep(**endpoint)
            for endpoint in api_context.get("global", {}).get("endpoints", [])
        ]

    def _get_field_bindings_for_global_context_data(
        self, api_context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Get the field bindings for the global context data."""
        return api_context.get("global", {}).get("fieldBindings", [])

    async def _prepare_global_variables_context_data(
        self,
        api_plan: List[CallStep],
        field_bindings: List[Dict[str, Any]],
        context_data: Dict[str, Any],
    ) -> Dict[str, Any]:
        """Execute the api plan and prepare the context data."""
        result = {}
        # Execute api plan and get the api data
        try:
            execution_context = await self.execute_api_plan(api_plan, context_data)
            context_data.update(execution_context.get("api_data", {}))
        except Exception as e:
            print(
                f"Error executing api for preparing global variables context data: {e}."
            )

        # Evaluate the field bindings and prepare the result
        result = {}
        for field_binding in field_bindings:
            try:
                field_name = field_binding.get("name", "")
                if field_name:
                    result.update(
                        ExpressionEvaluationUtil().evaluate_exp_in_json(
                            json_data={field_name: field_binding.get("value", "")},
                            input_data=context_data,
                        )
                    )
                else:
                    print(
                        f"Field binding name is not present for the field {field_binding.get('value', '')}"
                    )
            except Exception as e:
                print(
                    f"Error evaluating field binding: {e}. Skipping the field {field_binding.get('name', '')}."
                )
        return result

    async def _prepare_system_context_data(self) -> Dict[str, Any]:
        """Prepare the system context data for the query."""
        headers = await get_headers()
        return {"userId": headers.get("X-Jiffy-User-Id", "")}

    async def _prepare_global_context_data(self) -> Dict[str, Any]:
        """Prepare the context data for the query."""
        context = {}
        # Query context data
        context.update(self.context_data)
        # Global variables context data
        global_variables_context_data = (
            await self._prepare_global_variables_context_data(
                self.api_plan_for_global_context_data,
                self.field_bindings_for_global_context_data,
                self.context_data,
            )
        )
        context.update(global_variables_context_data)
        # System context data
        system_context_data = await self._prepare_system_context_data()
        context.update(system_context_data)
        # print("Global context data : \n", json.dumps(context, indent=2))
        return context

    async def expand_query(self, query: str) -> str:
        """Expand vague or shorthand queries into detailed, actionable queries."""
        try:
            # Load query expansion prompt
            query_expansion_prompt_path = Path(__file__).parent / "prompts" / "query_expansion_prompt.txt"
            with open(query_expansion_prompt_path, 'r') as f:
                prompt_template = f.read()
            
            # Format conversation history for the prompt
            conversation_history_text = ""
            if hasattr(self, 'conversation_history') and self.conversation_history:
                for msg in self.conversation_history:
                    role = "User" if msg.role == "user" else "Assistant"
                    # Extract text from parts
                    text = ""
                    if hasattr(msg, 'parts') and msg.parts:
                        text = " ".join([part.text for part in msg.parts if hasattr(part, 'text')])
                    conversation_history_text += f"{role}: {text}\n"
                
                # Remove the last user message from history if it's the same as current query
                # This prevents duplication since we show current query separately
                if self.conversation_history and self.conversation_history[-1].role == "user":
                    last_user_text = " ".join([part.text for part in self.conversation_history[-1].parts if hasattr(part, 'text')])
                    if last_user_text == query:
                        # Remove the last entry from formatted history
                        lines = conversation_history_text.strip().split('\n')
                        if lines and lines[-1].startswith("User:"):
                            conversation_history_text = '\n'.join(lines[:-1]) + '\n'
            
            if not conversation_history_text:
                conversation_history_text = "(No previous conversation)"
            
            # Replace placeholders with actual values
            prompt = prompt_template.replace("{{conversation_history}}", conversation_history_text.strip())
            prompt = prompt.replace("{{user_question}}", query)
            
            # Use GenAI to expand the query
            print(f"Original query: {query}")
            print("Expanding query with conversation context...")
            
            expansion_client = GenAI(
                instruction=prompt,
                model_name=ModelName.GEMINI_25_FLASH,
                temperature=0.1  # Low temperature for consistent expansions
            )
            
            result = await expansion_client.process()
            
            if result["status"] and result["data"]:
                expanded_query = result["data"]["output"].strip()
                print(f"Expanded query: {expanded_query}")
                return expanded_query
            else:
                print(f"Query expansion failed: {result.get('error', 'Unknown error')}")
                return query  # Return original query if expansion fails
                
        except Exception as e:
            print(f"Error during query expansion: {e}")
            traceback.print_exc()
            return query  # Return original query if error occurs

    async def run(
        self,
        query: str,
    ) -> Dict[str, Any]:
        """Main entry point for the agent.

        Args:
            query: The user's query.

        Returns:
        """
        self.query = query
        execution_start_time = time.time()

        # Default response
        default_response = {
            "data": [
                {
                    "type": "singlerow",
                    "text": "Sorry, I can't process this request now.",
                    "structured_data": {
                        "html": "<body> <p>Sorry, I can't process this request.</p> </body>"
                    },
                }
            ]
        }

        # Extract conversation history if present
        self.conversation_history = self._get_conv_history_for_gemini_models(
            query=query
        )

        # Prepare global context data including the context_data from the user
        self.global_context_data = await self._prepare_global_context_data()

        # Step 0: Expand the query
        print("\n--- Expanding Query ---")
        start_time = time.time()
        expanded_query = await self.expand_query(query)
        end_time = time.time()
        print(f"Query expansion completed in : {end_time - start_time} seconds")

        # Step 1: Generate Plan (Async) using expanded query
        print("\n--- Generating Plan ---")
        start_time = time.time()
        plan: List[CallStep] = await self.generate_api_plan_tool_call(expanded_query)
        end_time = time.time()
        print(f"Plan generation completed in : {end_time - start_time} seconds")

        # If no plan is generated, try to answer with the context data
        if not plan:
            print("No plan generated.")

        # If the plan is generated and the first call step has smallTalk, return the smallTalk
        is_small_talk = plan and getattr(plan[0], "smallTalk", None)
        execution_context = self.global_context_data

        if is_small_talk:
            print("Detected small talk. Not executing the plan.")
            
        if not is_small_talk:
            print("\n--- Executing Plan ---")
            start_time = time.time()
            execution_context = await self.execute_api_plan(
                plan, self.global_context_data
            )

            end_time = time.time()
            print(f"Plan execution completed in : {end_time - start_time} seconds")
            print("context data size : ", sys.getsizeof(json.dumps(execution_context)))

        # Step 3: Resolve Answer (Async) - use expanded query for better context
        print("\n--- Resolving Final Answer ---")
        start_time = time.time()
        final_answer = await self.resolve_final_answer(
            expanded_query,  # Use expanded query instead of original
            execution_context,
            self.field_bindings,
            self.table_bindings,
            self.chart_bindings,
            self.section_bindings,
            self.page_navigation_lookup,
        )
        end_time = time.time()
        print(f"Final answer resolution completed in : {end_time - start_time} seconds")

        execution_end_time = time.time()
        print(
            f"Execution completed in : {execution_end_time - execution_start_time} seconds"
        )
        # If the final answer is empty, return a default response
        if not self._is_valid_response(final_answer):
            final_answer = default_response
        print("result : \n", json.dumps(final_answer, indent=2))
        return final_answer



# Test conversation history with single value response
def get_conv_history_single_value_test_query():
    query_json = [
        {"actorType": "Human", "message": "show me the top 5 accounts"},
    ]
    query = json.dumps(query_json)
    return query


if __name__ == "__main__":
    try:
        query = "What stages are the 104/3567 onboarding requests in?"
        context_data = {}
        agent = ApiAgent(context_data=context_data)
        asyncio.run(agent.run(query))
    except Exception as e:
        print(f"An error occurred during the main test execution: {e}")
