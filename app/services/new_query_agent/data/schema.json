{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"Account": {"type": "object", "title": "Account", "description": "Account", "properties": {"accountCreationDetail": {"type": "string", "description": "Details pertaining to the creation process of the account, such as the initial setup method or specific notes from creation."}, "accountCustodianStatus": {"type": "string", "description": "The current status of the account with its custodian (e.g., 'Open', 'Closed', 'Pending Transfer', 'Frozen')."}, "accountIsSetUpForEDelivery": {"type": "boolean", "description": "Indicates whether the account is configured for electronic delivery of statements, trade confirmations, and other documents."}, "accountManagementType": {"type": "string", "description": "Specifies how the account is managed (e.g., 'Self-Directed', 'Advisor Man<PERSON>', 'Robo-Advisor')."}, "accountPrefix": {"type": "string", "description": "A unique prefix or identifier for the account, often used in internal systems or for grouping related accounts."}, "accountStatus": {"type": "string", "description": "The overall operational status of the account"}, "advisorTradingDiscretion": {"type": "string", "description": "Indicates the level of trading discretion granted to the advisor (e.g., 'Full Discretion', 'Limited Discretion', 'No Discretion')."}, "annualExpenses": {"type": "number", "description": "The estimated or actual total annual expenses of the account holder(s), excluding investments."}, "annualIncome": {"type": "number", "description": "The estimated or actual total annual gross income of the account holder(s)."}, "annualIncomeExact": {"type": "number", "description": "The precise total annual gross income of the account holder(s), if available."}, "assets": {"type": "number", "description": "The total value of all assets held by the account holder(s), including investments, real estate, etc."}, "cashDividendOption": {"type": "string", "description": "The instruction for how cash dividends should be handled (e.g., 'Reinvest', 'Pay to Bank', 'Hold in Cash')."}, "contingentBeneficiaries": {"type": "array", "description": "A list of individuals or entities designated as contingent beneficiaries, who would inherit assets if primary beneficiaries are deceased.", "items": {"type": "object"}}, "dividendReinvestmentOption": {"type": "string", "description": "The instruction for how dividends, specifically from equities, should be handled (e.g., 'Reinvest', 'Cash')."}, "dailyBalances": {"$ref": "#/definitions/AccountBalances", "description": "Represents the daily financial snapshot of the account, including ending market value, cash balance, money market balance, margin balance, and any maintenance or federal calls."}, "employeeAffiliationType": {"type": "string", "description": "Describes any employment-related affiliations that might impact the account (e.g., 'Publicly Traded Company', 'Broker-Dealer Employee')."}, "estimatedValueOfInvestments": {"type": "number", "description": "The estimated total current market value of all investments within the account."}, "estimatedValueOfInvestmentsExact": {"type": "number", "description": "The precise total current market value of all investments within the account, if available."}, "federalMarginalTaxRate": {"type": "number", "description": "The account holder's current federal marginal income tax rate, as a decimal (e.g., 0.24 for 24%)."}, "id": {"type": "string", "description": "The unique identifier for this specific account."}, "inheritedIRADistributionOption": {"type": "string", "description": "For inherited IRAs, specifies the chosen distribution method (e.g., '10-Year Rule', 'Life Expectancy')."}, "initialFundingSource": {"type": "string", "description": "The primary source of funds used to initially fund the account (e.g., 'Bank Transfer', 'Rollover', 'Check')."}, "initialFundingSources": {"type": "array", "description": "A list of all sources used for the initial funding of the account.", "items": {"type": "object"}}, "interestedParties": {"type": "array", "description": "A list of other individuals or entities who have an interest in the account but are not owners or beneficiaries (e.g., power of attorney, authorized traders).", "items": {"type": "object"}}, "investmentExperience": {"type": "string", "description": "The account holder's level of experience with various investments (e.g., 'Limited', 'Good', 'Extensive')."}, "investmentObjective": {"type": "string", "description": "Describes the client's primary financial goal for the account. Common values include 'Moderate Income', 'High Income', 'Growth', 'Balanced Growth and Income', and 'Capital Preservation'."}, "isInstitutionalAccount": {"type": "boolean", "description": "Indicates whether this account belongs to an institution (e.g., corporation, trust, non-profit) rather than an individual."}, "liabilities": {"type": "number", "description": "The total outstanding debts and financial obligations of the account holder(s)."}, "liquidAssets": {"type": "number", "description": "The value of assets that can be quickly converted to cash without significant loss of value (e.g., cash, savings accounts, highly marketable securities)."}, "liquidAssetsExact": {"type": "number", "description": "The precise value of liquid assets, if available."}, "liquidityNeeds": {"type": "string", "description": "Describes the account holder's need for accessible cash (e.g., 'Low', 'Moderate', 'High', 'Short-Term Horizon')."}, "marginRates": {"type": "array", "description": "Details of current margin interest rates applicable to the account.", "items": {"type": "object"}}, "moneyFundSweepOptIn": {"type": "boolean", "description": "Indicates whether uninvested cash in the account is automatically swept into a money market fund."}, "netWorthExcludingHome": {"type": "number", "description": "The total net worth of the account holder(s), excluding the value of their primary residence."}, "netWorthExcludingHomeExact": {"type": "number", "description": "The precise net worth excluding home, if available."}, "nickName": {"type": "string", "description": "A user-defined friendly name for the account (e.g., 'My Retirement', 'Kids College Fund')."}, "optionsRiskLevel": {"type": "string", "description": "The approved risk level for options trading on this account (e.g., 'Level 1: Covered Calls', 'Level 4: Naked Options')."}, "otherInitialFundingSource": {"type": "string", "description": "A free-text field for detailing any initial funding sources not covered by predefined options."}, "otherPrimaryInvestmentPurpose": {"type": "string", "description": "A free-text field for detailing any primary investment purposes not covered by predefined options."}, "primaryInvestmentPurpose": {"type": "string", "description": "The main reason for establishing and maintaining this investment account (e.g., 'Retirement', 'Education', 'Down Payment')."}, "primaryOwner": {"$ref": "#/definitions/PrimaryOwner"}, "product": {"type": "string", "description": "The specific financial product or type of account (e.g., 'Brokerage Account', 'IRA', '401k', '529 Plan')."}, "registrationType": {"$ref": "#/definitions/RegistrationType", "description": "This object is defined to understand the type of the account."}, "repCode": {"type": "string", "description": "The code of the representative or advisor associated with this account."}, "repCodeLink": {"$ref": "#/definitions/RepCodeLink"}, "riskTolerance": {"type": "string", "description": "The account holder's willingness to take on investment risk (e.g., 'Conservative', 'Moderate', 'Aggressive', 'Growth')."}, "salesProceedsDistribution": {"type": "string", "description": "Instructions for how proceeds from asset sales should be distributed (e.g., 'Reinvest', 'Withdraw to Bank', 'Hold in Cash')."}, "sePIRAEmployeeMinimumAge": {"type": "integer", "description": "For SEP IRAs, the minimum age an employee must be to participate."}, "sePIRAEmployeeMinimumEmploymentYears": {"type": "integer", "description": "For SEP IRAs, the minimum number of years of employment an employee must have to participate."}, "sePIRAIncludeCertainNonResidentAliens": {"type": "boolean", "description": "For SEP IRAs, indicates if certain non-resident aliens are included in the plan."}, "sePIRAIncludeCollectiveBargaining": {"type": "boolean", "description": "For SEP IRAs, indicates if employees covered by a collective bargaining agreement are included."}, "sePIRAIncludeEmployeesUnder450": {"type": "boolean", "description": "For SEP IRAs, indicates if employees earning under $450 (or current threshold) are included."}, "secondaryOwners": {"type": "array", "description": "A list of secondary account holders for joint accounts.", "items": {"type": "object"}}, "shareOwnerInformationWithOwnedCorporatio": {"type": "boolean", "description": "Indicates whether the owner's information can be shared with a corporation they own, relevant for certain business accounts."}, "sourceAccountQualification": {"type": "string", "description": "Describes any qualifications or restrictions on the source account for transfers or rollovers (e.g., 'Qualified Retirement Plan', 'Taxable Brokerage')."}, "specialExpenses": {"type": "number", "description": "Specific, significant upcoming expenses that the account holder anticipates (e.g., college tuition, large medical bills)."}, "specialExpensesTimeframe": {"type": "string", "description": "The expected timeframe for the special expenses (e.g., 'Short Term', 'Medium Term', 'Long Term', 'Specific Date')."}, "subType": {"type": "string", "description": "A more specific classification of the account beyond the 'product' type (e.g., for 'IRA', subType could be 'Traditional', 'Roth', 'SEP')."}, "timeHorizon": {"type": "string", "description": "The expected length of time the investments in the account will be held (e.g., 'Short-Term', 'Medium-Term', 'Long-Term', 'Indefinite')."}, "tradingPrivilege": {"type": "string", "description": "The overall trading privileges granted to the account (e.g., 'Equity Only', 'Options Enabled', 'Futures Enabled')."}, "tradingPrivileges": {"type": "array", "description": "A detailed list of specific trading privileges enabled for the account.", "items": {"type": "object"}}, "transfers": {"type": "array", "description": "A historical record or list of pending transfers into or out of the account.", "items": {"type": "object"}}, "wantBeneficiaries": {"type": "boolean", "description": "Indicates if the account holder desires to designate beneficiaries for the account."}}}, "AccountBalances": {"type": "object", "title": "AccountBalances", "description": "Account Balances", "properties": {"accruedDividends": {"type": "number", "description": "Dividends that have been earned but not yet paid to the account."}, "accruedInterestPurchases": {"type": "number", "description": "Interest accumulated on bond purchases between coupon payments."}, "accruedInterestSales": {"type": "number", "description": "Interest accumulated on bond sales that the buyer owes to the seller."}, "accumulatedFedCall": {"type": "integer", "description": "The total federal call amount that has accumulated on the account."}, "availableFundsToTrade": {"type": "number", "description": "The amount of funds immediately available for new trades."}, "availableFundsToWithdraw": {"type": "number", "description": "The amount of funds that can be immediately withdrawn from the account."}, "beginningBalance": {"type": "number", "description": "The total account balance at the beginning of a specified period."}, "beginningBuyingPower": {"type": "number", "description": "The buying power available at the beginning of a period."}, "beginningCashBalance": {"type": "number", "description": "The cash balance at the beginning of a specified period."}, "beginningMarginBalance": {"type": "number", "description": "The margin balance at the beginning of a specified period."}, "beginningMarketValue": {"type": "number", "description": "The total market value of all holdings at the beginning of a specified period."}, "beginningMoneyMarketBalance": {"type": "number", "description": "The balance held in money market funds at the beginning of a specified period."}, "bulkLoadRecIdJfyApx": {"type": "string", "description": "An identifier for the bulk load record related to JfyApx (likely an internal system ID)."}, "bulkLoadRunIdJfyApx": {"type": "string", "description": "An identifier for the bulk load run related to JfyApx (likely an internal system ID)."}, "cashAccountCashAvailable": {"type": "number", "description": "The available cash in the cash portion of the account."}, "cashAccountMarginValue": {"type": "number", "description": "The margin value associated with the cash account (if any, typically zero)."}, "cashAccountMarketValue": {"type": "number", "description": "The total market value of assets in the cash account."}, "cashAccountSymbol": {"type": "string", "description": "The symbol or identifier for the cash account itself."}, "cashManagementDDANumber": {"type": "string", "description": "The Demand Deposit Account (DDA) number for cash management features."}, "commission": {"type": "number", "description": "Total commission fees incurred during the period."}, "corporateInterest": {"type": "number", "description": "Interest earned from corporate bonds or other corporate debt."}, "creditInterest": {"type": "number", "description": "Interest paid to the account holder on cash balances."}, "dayTradeBuyingPower": {"type": "number", "description": "The buying power available for day trading activity."}, "dividends": {"type": "number", "description": "Total dividends received during the period."}, "endingBalance": {"type": "number", "description": "The total account balance at the end of a specified period."}, "endingBuyingPower": {"type": "number", "description": "The buying power available at the end of a period."}, "endingCashBalance": {"type": "number", "description": "The cash balance at the end of a specified period."}, "endingMarginBalance": {"type": "number", "description": "The margin balance at the end of a specified period."}, "endingMarketValue": {"type": "number", "description": "The total market value of all holdings at the end of a specified period."}, "endingMoneyMarketBalance": {"type": "number", "description": "The balance held in money market funds at the end of a specified period."}, "fedCall": {"type": "number", "description": "A margin call issued by the Federal Reserve."}, "fundsFrozenForChecks": {"type": "number", "description": "Funds that are temporarily unavailable due to pending check clearings."}, "governmentInterest": {"type": "number", "description": "Interest earned from government bonds or other government debt."}, "houseCall": {"type": "number", "description": "A margin call issued by the brokerage firm (house)."}, "id": {"type": "string", "description": "The unique identifier for this specific balance record."}, "interest": {"type": "number", "description": "Total interest earned or paid during the period."}, "intraDayTimestamp": {"type": "string", "description": "A timestamp indicating when the intra-day balance was recorded (useful for real-time data)."}, "liquidationValue": {"type": "number", "description": "The estimated value if all assets in the account were to be liquidated."}, "longMarketValue": {"type": "number", "description": "The total market value of all long positions in the account."}, "longTermCapitalGains": {"type": "number", "description": "Profits from the sale of assets held for more than one year."}, "maintenanceCall": {"type": "number", "description": "A margin call to restore the account to its minimum maintenance margin requirement."}, "marginAccountCashAvailable": {"type": "number", "description": "The available cash in the margin portion of the account."}, "marginEquityAmount": {"type": "number", "description": "The dollar amount of equity in the margin account."}, "marginEquityPercent": {"type": "number", "description": "The percentage of equity in the margin account."}, "marketAppreciation": {"type": "number", "description": "The increase in the market value of holdings due to price changes."}, "miscellaneousCreditOrDebit": {"type": "number", "description": "Any miscellaneous credits or debits applied to the account."}, "moneyMarketInterest": {"type": "number", "description": "Interest earned from money market funds."}, "municipalInterestTax": {"type": "number", "description": "Interest earned from municipal bonds, which is often tax-exempt."}, "nonQualifiedDividends": {"type": "number", "description": "Dividends that do not meet IRS criteria for qualified dividends and are taxed at ordinary income rates."}, "otherIncome": {"type": "number", "description": "Any other forms of income generated within the account not categorized elsewhere."}, "partnershipDistributions": {"type": "number", "description": "Distributions received from partnership investments."}, "periodEndDate": {"type": "string", "format": "date", "description": "The end date of the period for which these balances are reported."}, "periodStartDate": {"type": "string", "format": "date", "description": "The start date of the period for which these balances are reported."}, "periodType": {"type": "string", "description": "The type of period for the balances (e.g., 'Daily', 'Monthly', 'Quarterly', 'Annual')."}, "previousAuthorizationLimit": {"type": "number", "description": "The previous limit for authorized transactions or withdrawals."}, "principalPayments": {"type": "number", "description": "Payments received that reduce the principal amount of a debt or loan."}, "qualifiedDividends": {"type": "number", "description": "Dividends that meet IRS criteria for qualified dividends and are taxed at preferential rates."}, "recentDeposits": {"type": "number", "description": "The total amount of recent deposits made to the account."}, "recordSource": {"type": "string", "description": "The source system or method from which this balance record was derived."}, "regulationTBuyingPower": {"type": "number", "description": "The buying power calculated under Federal Reserve Board's Regulation T."}, "repurchaseInterest": {"type": "number", "description": "Interest earned from repurchase agreements (repos)."}, "returnOfCapital": {"type": "number", "description": "A distribution to shareholders that is considered a return of their original investment, not a dividend or capital gain."}, "royaltyPayments": {"type": "number", "description": "Payments received from royalties (e.g., from intellectual property)."}, "settlementDateBalance": {"type": "number", "description": "The total account balance based on settlement date (when trades officially clear)."}, "settlementDateCashBalance": {"type": "number", "description": "The cash balance based on settlement date."}, "settlementDateFeeBalance": {"type": "number", "description": "The fee balance based on settlement date."}, "settlementDateMarginBalance": {"type": "number", "description": "The margin balance based on settlement date."}, "settlementDateShortBalance": {"type": "number", "description": "The short position balance based on settlement date."}, "shortMarketValue": {"type": "number", "description": "The total market value of all short positions in the account."}, "shortTermCapitalGains": {"type": "number", "description": "Profits from the sale of assets held for one year or less."}, "smABalance": {"type": "number", "description": "The Special Memorandum Account (SMA) balance, representing the excess equity in a margin account."}, "substitutePayments": {"type": "number", "description": "Payments received in lieu of dividends or interest, typically from securities lending."}, "tradeDateBalance": {"type": "number", "description": "The total account balance based on trade date (when trades are executed)."}, "tradeDateCashBalance": {"type": "number", "description": "The cash balance based on trade date."}, "tradeDateMarginBalance": {"type": "number", "description": "The margin balance based on trade date."}, "tradeDateShortBalance": {"type": "number", "description": "The short position balance based on trade date."}, "withdrawals": {"type": "number", "description": "Total withdrawals made from the account during the period."}}}, "EmployerContact": {"type": "object", "title": "EmployerContact", "description": "Contact information for the account holder's employer, relevant for certain account types like 401(k)s.", "properties": {"name": {"type": "string"}, "address": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}}}, "JointTenancyState": {"type": "object", "title": "JointTenancyState", "description": "For joint accounts, describes the state where the joint tenancy is established, which can impact legal and tax implications.", "properties": {"id": {"type": "string", "description": "The unique identifier for the state."}, "name": {"type": "string", "description": "The full name of the state (e.g., 'California', 'New York')."}}}, "PrecedingOwner": {"type": "object", "title": "PrecedingOwner", "description": "Information about the previous owner of the account, relevant for inherited accounts or transfers.", "properties": {"id": {"type": "string"}, "name": {"type": "string"}, "relationshipToCurrentOwner": {"type": "string"}}}, "PrimaryOwner": {"type": "object", "title": "PrimaryOwner", "description": "Details of the primary account holder.", "properties": {"id": {"type": "string", "description": "The unique identifier."}}}, "RegistrationType": {"type": "object", "title": "RegistrationType", "description": "The legal and tax registration type of the account.", "properties": {"code": {"type": "string", "description": "A short code representing the registration type (e.g., 'IND' for Individual, 'JOINT' for Joint Tenants)."}, "id": {"type": "string", "description": "The unique identifier for the registration type."}, "isRetirement": {"type": "boolean", "description": "Indicates if this registration type is for a retirement account (e.g., IRA, 401k)."}, "name": {"type": "string", "description": "The full descriptive name of the registration type (e.g., 'Individual', 'Joint Tenants with Right of Survivorship')."}}}, "RepCodeLink": {"type": "object", "title": "RepCodeLink", "description": "Details linking to the representative or advisor associated with the account.", "properties": {"id": {"type": "string", "description": "The unique identifier of the representative."}, "repCode": {"type": "string", "description": "The representative's code."}, "repName": {"type": "string", "description": "The full name of the representative."}}}, "Client": {"type": "object", "properties": {"id": {"type": "string"}}}}}