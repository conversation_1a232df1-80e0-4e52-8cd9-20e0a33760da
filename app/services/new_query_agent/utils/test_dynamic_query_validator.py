#!/usr/bin/env python3
"""
Comprehensive test suite for the Dynamic Query Validator

This test suite covers:
1. Basic structural validation
2. Operation type validation
3. Data type validation
4. Schema-based table validation
5. Schema-based column validation
6. Nested relationship validation
7. GroupBy validation
8. Error handling and edge cases

Usage:
    python -m pytest app/services/new_query_agent/utils/test_dynamic_query_validator.py -v
    
    Or run directly:
    python app/services/new_query_agent/utils/test_dynamic_query_validator.py
"""

import unittest
import json
import os
import tempfile
from typing import Dict, Any

from dynamic_query_validator import QueryValidator, validate_query, ValidationError


class TestQueryValidator(unittest.TestCase):
    """Test cases for the QueryValidator class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.validator = QueryValidator()
        
        # Create a test schema for schema validation tests
        self.test_schema = {
            "$schema": "http://json-schema.org/draft-07/schema#",
            "definitions": {
                "Account": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "accountStatus": {"type": "string"},
                        "annualIncome": {"type": "number"},
                        "primaryOwner": {"$ref": "#/definitions/PrimaryOwner"},
                        "dailyBalances": {"$ref": "#/definitions/AccountBalances"}
                    }
                },
                "PrimaryOwner": {
                    "type": "object",
                    "properties": {
                        "id": {"type": "string"},
                        "firstName": {"type": "string"},
                        "lastName": {"type": "string"}
                    }
                },
                "AccountBalances": {
                    "type": "object",
                    "properties": {
                        "endingBalance": {"type": "number"},
                        "endingCashBalance": {"type": "number"}
                    }
                }
            }
        }
        
        # Create temporary schema file
        self.schema_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        json.dump(self.test_schema, self.schema_file)
        self.schema_file.close()
        
        # Create validator with schema
        self.schema_validator = QueryValidator(schema_path=self.schema_file.name)
    
    def tearDown(self):
        """Clean up test fixtures"""
        if os.path.exists(self.schema_file.name):
            os.unlink(self.schema_file.name)


class TestBasicStructuralValidation(TestQueryValidator):
    """Test basic structural validation"""
    
    def test_valid_basic_query(self):
        """Test a valid basic query structure"""
        query = {
            "Account": {
                "select": {"id": True, "accountStatus": True},
                "filter": "accountStatus == 'Active'"
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_empty_query(self):
        """Test empty query validation"""
        query = {}
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "EMPTY_QUERY")
    
    def test_invalid_json_string(self):
        """Test invalid JSON string"""
        invalid_json = '{"Account": {"select": {"id": true}'  # Missing closing braces
        is_valid, errors = self.validator.validate(invalid_json)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "JSON_PARSE_ERROR")
    
    def test_non_dict_structure(self):
        """Test non-dictionary structure"""
        query = ["not", "a", "dict"]
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "TYPE_ERROR")


class TestOperationValidation(TestQueryValidator):
    """Test operation type validation"""
    
    def test_valid_operations(self):
        """Test all valid operations"""
        query = {
            "Account": {
                "select": {"id": True},
                "filter": "id != null",
                "orderBy": "id",
                "groupBy": ["accountStatus"],
                "limit": 10,
                "offset": 5
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_invalid_operation(self):
        """Test invalid operation type"""
        query = {
            "Account": {
                "select": {"id": True},
                "invalidOperation": "test"
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "INVALID_OPERATION")
    
    def test_distinct_operation_conflict(self):
        """Test distinct operation conflicts with other operations"""
        query = {
            "Account": {
                "distinct": {"id": True},
                "select": {"accountStatus": True}  # Should conflict with distinct
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "OPERATION_CONFLICT")


class TestDataTypeValidation(TestQueryValidator):
    """Test data type validation for operations"""
    
    def test_filter_wrong_type(self):
        """Test filter with wrong data type"""
        query = {
            "Account": {
                "select": {"id": True},
                "filter": {"should": "be_string"}  # Should be string
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "TYPE_ERROR")
    
    def test_limit_wrong_type(self):
        """Test limit with wrong data type"""
        query = {
            "Account": {
                "select": {"id": True},
                "limit": "should_be_number"  # Should be int
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "TYPE_ERROR")
    
    def test_groupby_wrong_type(self):
        """Test groupBy with wrong data type"""
        query = {
            "Account": {
                "select": {"id": True},
                "groupBy": "should_be_list"  # Should be list
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "TYPE_ERROR")


class TestSelectStructureValidation(TestQueryValidator):
    """Test SELECT operation structure validation"""
    
    def test_valid_select_structure(self):
        """Test valid SELECT structure"""
        query = {
            "Account": {
                "select": {
                    "id": True,
                    "accountStatus": False,
                    "primaryOwner": {
                        "select": {"firstName": True},
                        "filter": "firstName != null"
                    }
                }
            }
        }
        # Use schema validator for this test since it involves real column names
        is_valid, errors = self.schema_validator.validate(query)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_select_wrong_type(self):
        """Test SELECT with wrong type"""
        query = {
            "Account": {
                "select": "should_be_dict"  # Should be dict
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "TYPE_ERROR")
    
    def test_select_field_wrong_type(self):
        """Test SELECT field with wrong type"""
        query = {
            "Account": {
                "select": {
                    "id": "should_be_bool_or_dict"  # Should be bool or dict
                }
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "TYPE_ERROR")
    
    def test_nested_select_invalid_keys(self):
        """Test nested SELECT with invalid keys"""
        query = {
            "Account": {
                "select": {
                    "primaryOwner": {
                        "select": {"firstName": True},
                        "invalidKey": "value"  # Only 'select' and 'filter' allowed
                    }
                }
            }
        }
        # Use schema validator to avoid column validation errors
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        # Should have at least one INVALID_KEYS error
        error_types = [error.error_type for error in errors]
        self.assertIn("INVALID_KEYS", error_types)


class TestGroupByValidation(TestQueryValidator):
    """Test groupBy validation"""
    
    def test_valid_groupby(self):
        """Test valid groupBy"""
        query = {
            "Account": {
                "select": {"id": True},
                "groupBy": ["accountStatus", "annualIncome"]
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_groupby_nested_field_error(self):
        """Test groupBy with nested field paths (should be invalid)"""
        query = {
            "Account": {
                "select": {"id": True},
                "groupBy": ["primaryOwner.firstName"]  # Nested paths not allowed
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "NESTED_FIELD_ERROR")
    
    def test_groupby_non_string_field(self):
        """Test groupBy with non-string field"""
        query = {
            "Account": {
                "select": {"id": True},
                "groupBy": ["accountStatus", 123]  # Should be string
            }
        }
        is_valid, errors = self.validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "TYPE_ERROR")


class TestSchemaValidation(TestQueryValidator):
    """Test schema-based validation"""

    def test_valid_table_name(self):
        """Test valid table name against schema"""
        query = {
            "Account": {
                "select": {"id": True}
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

    def test_invalid_table_name(self):
        """Test invalid table name against schema"""
        query = {
            "NonExistentTable": {
                "select": {"id": True}
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "INVALID_TABLE")
        self.assertIn("NonExistentTable", errors[0].message)
        self.assertIn("Account", errors[0].message)  # Should suggest valid tables

    def test_valid_column_name(self):
        """Test valid column name against schema"""
        query = {
            "Account": {
                "select": {"id": True, "accountStatus": True}
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

    def test_invalid_column_name(self):
        """Test invalid column name against schema"""
        query = {
            "Account": {
                "select": {"id": True, "nonExistentColumn": True}
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "INVALID_COLUMN")
        self.assertIn("nonExistentColumn", errors[0].message)
        # Should show only direct columns (no nested ones)
        self.assertIn("id", errors[0].message)
        self.assertIn("accountStatus", errors[0].message)
        self.assertNotIn("primaryOwner.firstName", errors[0].message)

    def test_valid_nested_relationship(self):
        """Test valid nested relationship"""
        query = {
            "Account": {
                "select": {
                    "id": True,
                    "primaryOwner": {
                        "select": {"firstName": True, "lastName": True}
                    }
                }
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)

    def test_invalid_nested_column(self):
        """Test invalid column in nested relationship"""
        query = {
            "Account": {
                "select": {
                    "id": True,
                    "primaryOwner": {
                        "select": {"firstName": True, "invalidField": True}
                    }
                }
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "INVALID_COLUMN")
        self.assertIn("invalidField", errors[0].message)
        self.assertIn("PrimaryOwner", errors[0].message)

    def test_groupby_column_validation(self):
        """Test groupBy column validation against schema"""
        query = {
            "Account": {
                "select": {"id": True},
                "groupBy": ["accountStatus", "invalidColumn"]
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "INVALID_COLUMN")
        self.assertIn("invalidColumn", errors[0].message)

    def test_insert_column_validation(self):
        """Test INSERT operation column validation"""
        query = {
            "Account": {
                "insert": {
                    "id": "123",
                    "accountStatus": "Active",
                    "invalidColumn": "value"
                }
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "INVALID_COLUMN")
        self.assertIn("invalidColumn", errors[0].message)

    def test_update_column_validation(self):
        """Test UPDATE operation column validation"""
        query = {
            "Account": {
                "update": {
                    "accountStatus": "Inactive",
                    "invalidColumn": "value"
                }
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "INVALID_COLUMN")
        self.assertIn("invalidColumn", errors[0].message)

    def test_insert_list_validation(self):
        """Test INSERT operation with list of objects"""
        query = {
            "Account": {
                "insert": [
                    {"id": "123", "accountStatus": "Active"},
                    {"id": "456", "invalidColumn": "value"}
                ]
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        self.assertEqual(len(errors), 1)
        self.assertEqual(errors[0].error_type, "INVALID_COLUMN")
        self.assertIn("invalidColumn", errors[0].message)


class TestEdgeCases(TestQueryValidator):
    """Test edge cases and error handling"""

    def test_validator_without_schema(self):
        """Test validator without schema (should not perform schema validation)"""
        query = {
            "SomeTable": {
                "select": {"someColumn": True}
            }
        }
        # Create a validator explicitly without schema
        no_schema_validator = QueryValidator(schema_path="/nonexistent/path")
        is_valid, errors = no_schema_validator.validate(query)
        self.assertTrue(is_valid)  # No schema validation, so should pass structural validation
        self.assertEqual(len(errors), 0)

    def test_schema_file_not_found(self):
        """Test behavior when schema file doesn't exist"""
        validator = QueryValidator(schema_path="/nonexistent/path/schema.json")
        query = {
            "Account": {
                "select": {"id": True}
            }
        }
        # Should gracefully handle missing schema file
        is_valid, errors = validator.validate(query)
        self.assertTrue(is_valid)  # Should pass without schema validation
        self.assertEqual(len(errors), 0)

    def test_multiple_errors(self):
        """Test query with multiple validation errors"""
        query = {
            "Account": {
                "select": {"invalidColumn1": True, "invalidColumn2": True},
                "invalidOperation": "test",
                "limit": "should_be_number"
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 1)  # Should have multiple errors

    def test_deeply_nested_structure(self):
        """Test deeply nested query structure"""
        query = {
            "Account": {
                "select": {
                    "id": True,
                    "primaryOwner": {
                        "select": {
                            "firstName": True,
                            "nested": {
                                "select": {"deepField": True}
                            }
                        }
                    }
                }
            }
        }
        is_valid, errors = self.schema_validator.validate(query)
        # Should handle nested structures gracefully
        self.assertIsInstance(is_valid, bool)
        self.assertIsInstance(errors, list)


class TestConvenienceFunction(TestQueryValidator):
    """Test the convenience validate_query function"""

    def test_validate_query_function_with_schema(self):
        """Test validate_query convenience function with schema"""
        query = {
            "Account": {
                "select": {"id": True, "invalidColumn": True}
            }
        }
        result = validate_query(query, print_errors=False, schema_path=self.schema_file.name)
        self.assertFalse(result)

    def test_validate_query_function_without_schema(self):
        """Test validate_query convenience function without schema"""
        query = {
            "Account": {
                "select": {"id": True}
            }
        }
        result = validate_query(query, print_errors=False)
        self.assertTrue(result)

    def test_validate_query_with_json_string(self):
        """Test validate_query with JSON string input"""
        query_json = '{"Account": {"select": {"id": true}}}'
        result = validate_query(query_json, print_errors=False)
        self.assertTrue(result)

    def test_validate_query_with_invalid_json_string(self):
        """Test validate_query with invalid JSON string"""
        invalid_json = '{"Account": {"select": {"id": true}'  # Missing closing brace
        result = validate_query(invalid_json, print_errors=False)
        self.assertFalse(result)


def run_tests():
    """Run all tests"""
    unittest.main(verbosity=2)


if __name__ == "__main__":
    run_tests()
